<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Database Operations JavaScript -->
    <t t-name="scripts.database_operations">
        <script><![CDATA[
            // Database Operations Manager
            class DatabaseOperations {
                constructor() {
                    this.apiBaseUrl = '/api/databases';
                    this.isLoading = false;
                }

                // Show loading state on a card
                setCardLoading(dbName, isLoading, message = 'Loading...') {
                    const card = document.querySelector(`[data-dbname="${dbName}"]`);
                    if (!card) return;

                    const button = card.querySelector('button');
                    if (!button) return;

                    if (isLoading) {
                        button.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${message}`;
                        button.disabled = true;
                        button.classList.add('opacity-75');
                    } else {
                        button.disabled = false;
                        button.classList.remove('opacity-75');
                        // Restore original button content based on database state
                        const isInitialized = card.querySelector('.bg-green-100');
                        if (isInitialized) {
                            button.innerHTML = '<i class="fas fa-sign-in-alt text-sm"></i><span>Connect</span><i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>';
                        } else {
                            button.innerHTML = '<i class="fas fa-cog text-sm"></i><span>Initialize</span><i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>';
                        }
                    }
                }

                // Create a new database
                async handleCreateDatabase(event) {
                    event.preventDefault();

                    if (this.isLoading) return;
                    this.isLoading = true;

                    const formData = new FormData(event.target);
                    const data = {
                        name: formData.get('name'),
                        language: formData.get('language'),
                        demo: formData.has('demo')
                    };

                    // Validate database name
                    if (!data.name || !data.name.match(/^[a-zA-Z0-9_-]+$/)) {
                        alert('Database name must contain only letters, numbers, underscores, and hyphens.');
                        this.isLoading = false;
                        return;
                    }

                    try {
                        const response = await fetch(this.apiBaseUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            hideCreateDatabaseModal();
                            this.showSuccessMessage('Database created successfully! Redirecting...');
                            
                            // Redirect to the new database
                            setTimeout(() => {
                                window.location.href = '/home?db=' + encodeURIComponent(data.name);
                            }, 1500);
                        } else {
                            throw new Error(result.detail || result.message || 'Failed to create database');
                        }
                    } catch (error) {
                        console.error('Error creating database:', error);
                        this.showErrorMessage('Failed to create database: ' + error.message);
                    } finally {
                        this.isLoading = false;
                    }
                }

                // Connect to a database
                connectToDatabase(dbName) {
                    if (this.isLoading) return;
                    
                    this.setCardLoading(dbName, true, 'Connecting...');
                    
                    // Navigate to the database
                    window.location.href = '/home?db=' + encodeURIComponent(dbName);
                }

                // Initialize a database
                initializeDatabase(dbName) {
                    if (this.isLoading) return;

                    const confirmed = confirm('This will initialize the database with the base module. This process may take a few minutes. Continue?');
                    if (!confirmed) return;

                    this.setCardLoading(dbName, true, 'Initializing...');
                    
                    // Show initialization message
                    alert('Database initialization will begin when you connect. Please wait for the process to complete.');
                    
                    // Navigate to trigger initialization
                    window.location.href = '/home?db=' + encodeURIComponent(dbName);
                }

                // Delete a database
                async deleteDatabase(dbName) {
                    if (this.isLoading) return;

                    const confirmed = confirm(`Are you sure you want to delete database "${dbName}"? This action cannot be undone.`);
                    if (!confirmed) return;

                    const confirmText = prompt('This will permanently delete all data in the database. Type "DELETE" to confirm:');
                    if (confirmText !== 'DELETE') {
                        if (confirmText !== null) {
                            alert('Deletion cancelled. You must type "DELETE" exactly to confirm.');
                        }
                        return;
                    }

                    this.isLoading = true;
                    this.setCardLoading(dbName, true, 'Deleting...');

                    try {
                        const response = await fetch(`${this.apiBaseUrl}/${encodeURIComponent(dbName)}`, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            this.showSuccessMessage('Database deleted successfully');
                            
                            // Remove the card from the UI
                            const card = document.querySelector(`[data-dbname="${dbName}"]`);
                            if (card) {
                                card.style.transition = 'all 0.3s ease-out';
                                card.style.transform = 'scale(0.95)';
                                card.style.opacity = '0';
                                setTimeout(() => {
                                    card.remove();
                                    this.updateDatabaseCount();
                                }, 300);
                            }
                        } else {
                            throw new Error(result.detail || result.message || 'Failed to delete database');
                        }
                    } catch (error) {
                        console.error('Error deleting database:', error);
                        this.showErrorMessage('Failed to delete database: ' + error.message);
                        this.setCardLoading(dbName, false);
                    } finally {
                        this.isLoading = false;
                    }
                }

                // Refresh the database list
                refreshDatabases() {
                    window.location.reload();
                }

                // Update database count in status bar
                updateDatabaseCount() {
                    const cards = document.querySelectorAll('[data-dbname]');
                    const count = cards.length;
                    const countBadge = document.querySelector('.bg-blue-100.text-blue-800 span');
                    if (countBadge) {
                        countBadge.textContent = `${count} Database${count === 1 ? '' : 's'}`;
                    }

                    // Show empty state if no databases
                    if (count === 0) {
                        const databaseList = document.getElementById('database-list');
                        if (databaseList) {
                            databaseList.innerHTML = `
                                <div class="text-center py-24 bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30 border-2 border-dashed border-gray-300 rounded-3xl shadow-inner">
                                    <div class="relative mx-auto mb-8">
                                        <div class="relative w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                                            <i class="fas fa-database text-white text-2xl"></i>
                                        </div>
                                    </div>
                                    <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-4">No Databases Found</h3>
                                    <p class="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                                        Get started by creating your first database. You can set up a new database with demo data or start with a clean slate.
                                    </p>
                                    <div class="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                                        <button class="inline-flex items-center px-6 py-3 rounded-xl text-base font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 shadow-lg hover:shadow-xl" onclick="showCreateDatabaseModal()">
                                            <i class="fas fa-plus mr-2"></i>
                                            Create Database
                                        </button>
                                        <button class="inline-flex items-center px-6 py-3 rounded-xl text-base font-medium text-gray-700 bg-white border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-300 shadow-lg hover:shadow-xl" onclick="databaseOps.refreshDatabases()">
                                            <i class="fas fa-sync-alt mr-2"></i>
                                            Refresh List
                                        </button>
                                    </div>
                                </div>
                            `;
                        }
                    }
                }

                // Show success message
                showSuccessMessage(message) {
                    // You can implement a toast notification system here
                    alert(message);
                }

                // Show error message
                showErrorMessage(message) {
                    // You can implement a toast notification system here
                    alert(message);
                }
            }

            // Global database operations instance
            const databaseOps = new DatabaseOperations();
        ]]></script>
    </t>
</templates>
